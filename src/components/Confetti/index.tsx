import { forwardRef, type ElementRef } from 'react'
import confetti from 'canvas-confetti'

const ConfettiExplosion = forwardRef<ElementRef<'button'>>((_, _ref) => {
  const triggerConfetti = () => {
    // Number of explosions
    const numberOfExplosions = 50

    // Set an initial delay for the first explosion
    const baseDelay = 100

    // Trigger multiple confetti explosions with delays
    for (let i = 0; i < numberOfExplosions; i++) {
      // Random delay for each explosion, increasing with each iteration
      const delay = baseDelay * i

      // Random position for each explosion
      const x = Math.random() * window.innerWidth // Random horizontal position
      const y = Math.random() * window.innerHeight // Random vertical position

      // Use setTimeout to delay the confetti explosion
      setTimeout(() => {
        confetti({
          particleCount: 100, // Number of particles
          spread: 70, // Spread of the confetti
          origin: { x: x / window.innerWidth, y: y / window.innerHeight }, // Position
          scalar: 0.5 // Size of particles
        })
      }, delay)
    }
  }

  return (
    <div>
      <button onClick={triggerConfetti} ref={_ref} className='hidden'>
        Trigger Confetti
      </button>
    </div>
  )
})

ConfettiExplosion.displayName = 'ConfettiExplosion'

export default ConfettiExplosion
