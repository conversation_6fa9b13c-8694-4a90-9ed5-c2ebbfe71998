import styled from 'styled-components'
import ShuffleIcon from '@mui/icons-material/Shuffle'
import SortByAlphaIcon from '@mui/icons-material/SortByAlpha'
import ColorLensIcon from '@mui/icons-material/ColorLens'
import Modal from '@mui/material/Modal'
import { useState, useRef, useMemo, useEffect } from 'react'
import { setNames, setColors, themes } from '../../store/wheel'
import { useAppDispatch, useAppSelector } from '../../hooks/store'

const Entries = ({ setRiggedName }) => {
  const dispatch = useAppDispatch()
  const { names, colors } = useAppSelector(state => state.wheel)
  const loading = useAppSelector(state => state.wheel.loading)
  const [showWinnerModal, setShowWinnerModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [showSearch, setShowSearch] = useState(false)
  const [tempNames, setTempNames] = useState('')
  const [isEditing, setIsEditing] = useState(false)

  const MAX_NAMES = 50

  const getSelectedArr = () => {
    let selected = 'Custom'
    Object.keys(themes).forEach(key => {
      if (JSON.stringify(themes[key]) === JSON.stringify(colors)) selected = key
    })
    return selected
  }
  const selectedKey = getSelectedArr()
  const [selected, setSelected] = useState(selectedKey)
  const [_custom, setCustom] = useState(selectedKey === 'Custom' ? colors : themes[selectedKey])

  const inputRef = useRef()

  const onNameChange = useMemo(() => {
    return (riggedName: string | undefined, namesArray: string[]) => {
      // We'll only set a rigged name if explicitly passed
      // (not from comma detection in the input field)
      if (riggedName) {
        // Remove commas from the rigged name for consistent matching
        const cleanRiggedName = riggedName.replace(/,/g, '')
        setRiggedName(cleanRiggedName)
      }

      // Filter out empty names and limit to MAX_NAMES
      const filteredNamesArray = namesArray
        .map(name => name.trim())
        .filter(name => name !== '')
        .slice(0, MAX_NAMES)

      // Process all names - ensure only one comma per line
      const cleanNames = filteredNamesArray
        .map((name: string) => {
          // Trim whitespace
          const trimmedName = name.trim()

          // Ensure only one comma per line (same logic as removeCommaFromString)
          const firstCommaIndex = trimmedName.indexOf(',')
          if (firstCommaIndex === -1) return trimmedName // No comma in this line

          // Keep everything before the first comma, the comma itself, and everything after but with commas removed
          const beforeComma = trimmedName.substring(0, firstCommaIndex)
          const afterComma = trimmedName.substring(firstCommaIndex + 1).replace(/,/g, '')
          return beforeComma + ',' + afterComma
        })
        .join('\n')

      localStorage.setItem(
        'names',
        cleanNames.trim() !== ''
          ? cleanNames
          : 'Ali\nBeatriz\nCharles\nDiya\nEric\nFatima\nGabriel\nHanna\n'
      )
      dispatch(setNames(cleanNames))

      // Only show feedback if not setting a rigged name
      if (!riggedName && namesArray.length > 0) {
      }
    }
  }, [dispatch, setRiggedName, MAX_NAMES])


  const handleSortClick = () => {
    const namesArray = names.split('\n')
    let filteredNames = namesArray.filter(name => name !== '')

    // Sort the names with numbers first, then text alphabetically
    filteredNames.sort((a, b) => {
      // For sorting purposes, use the part before the comma if it exists
      const aForSort = a.split(',')[0].trim()
      const bForSort = b.split(',')[0].trim()

      const numA = parseFloat(aForSort)
      const numB = parseFloat(bForSort)

      // Check if both are numbers
      const isNumA = !isNaN(numA)
      const isNumB = !isNaN(numB)

      // If both are numbers, sort numerically
      if (isNumA && isNumB) {
        return numA - numB
      }

      // If a is a number and b is not, a comes first
      if (isNumA) return -1

      // If b is a number and a is not, b comes first
      if (isNumB) return 1

      // Otherwise, sort alphabetically
      return aForSort.toLowerCase().localeCompare(bForSort.toLowerCase())
    })

    dispatch(setNames(filteredNames.join('\n')))
    // setFeedback({ show: true, message: 'Entries sorted' })
  }

  const handleShuffleClick = () => {
    const namesArray = names.split('\n')
    const filteredNames = namesArray.filter(name => name !== '')
    const shuffledNames = filteredNames.sort(() => Math.random() - 0.5)
    dispatch(setNames(shuffledNames.join('\n')))
  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only process if not in an input field
      if ((e.target as HTMLElement).tagName !== 'TEXTAREA' && (e.target as HTMLElement).tagName !== 'INPUT') {
        if (e.ctrlKey || e.metaKey) {
          if (e.key === 'f') {
            e.preventDefault()
            setShowSearch(prev => !prev)
          } else if (e.key === 's') {
            e.preventDefault()
            handleSortClick()
          } else if (e.key === 'r') {
            e.preventDefault()
            handleShuffleClick()
          }
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown as any)
    return () => window.removeEventListener('keydown', handleKeyDown as any)
  }, [handleSortClick, handleShuffleClick])
  }


  // Create display version of names (without commas for user viewing)
  const displayNames = useMemo(() => {
    return names.split('\n').map(name => name.replace(/,/g, '')).join('\n');
  }, [names])

  // Initialize tempNames when not editing
  useEffect(() => {
    if (!isEditing) {
      setTempNames(displayNames);
    }
  }, [displayNames, isEditing])



  // Filter entries based on search term
  const filteredNames = useMemo(() => {
    if (!searchTerm) return displayNames

    const displayNamesArray = displayNames.split('\n')
    const filtered = displayNamesArray.filter(name => {
      // Search in the display name (without commas)
      return name.toLowerCase().includes(searchTerm.toLowerCase())
    })
    return filtered.join('\n')
  }, [displayNames, searchTerm])
  const handleChangeColorClick = () => {
    setShowWinnerModal(true)
  }




  return (
    <ControlsContainer>
      <Modal
        open={showWinnerModal}
        onClose={() => {
          setShowWinnerModal(false)
        }}
        aria-labelledby='modal-modal-title'
        aria-describedby='modal-modal-description'
      >
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: 400,
            borderRadius: 10,
            overflow: 'hidden',
            backgroundColor: '#12151C',
            padding: '20px',
            maxHeight: '500px',
            overflowY: 'scroll'
          }}
        >
          {Object.entries(themes).map(([key, value]) => (
            <div key={key}>
              <p className='mb-2'>{key}</p>
              {Object.entries(value).map(([title, colorSet]) => (
                <Menu
                  key={title}
                  onClick={() => {
                    dispatch(setColors(colorSet))
                    localStorage.setItem('theme', JSON.stringify(colorSet))
                    setSelected(title)
                    setCustom(colorSet)
                    // setShowWinnerModal(false)
                  }}
                  style={{ background: selected === key ? '#272e3d' : '' }}
                >
                  <p style={{ width: '200px' }}>{title}:</p>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                      gap: '10px'
                    }}
                  >
                    {colorSet.map((color, i) => (
                      <div
                        key={i}
                        style={{
                          borderRadius: 5,
                          height: '20px',
                          width: '20px',
                          background: color,
                          border: '1px solid white'
                        }}
                      />
                    ))}
                  </div>
                </Menu>
              ))}
            </div>
          ))}
        </div>
      </Modal>
      <ButtonContainer>
        <Button onClick={handleShuffleClick}>
          <ShuffleIcon sx={{ marginRight: '2px', width: '20px' }} />
          Shuffle
        </Button>
        <Button onClick={handleSortClick}>
          <SortByAlphaIcon sx={{ marginRight: '2px', width: '20px' }} />
          Sort
        </Button>
        <Button onClick={handleChangeColorClick}>
          <ColorLensIcon sx={{ marginRight: '2px', width: '20px' }} />
          Customize
        </Button>
      </ButtonContainer>
      <InputBox
        disabled={loading}
        ref={inputRef}
        placeholder='Enter names, each on a new line'
        value={searchTerm ? filteredNames : tempNames.replace(/,/g, '')}
        onFocus={() => {
          setIsEditing(true)
          if (searchTerm) {
            setSearchTerm('')
          }
        }}
        onChange={e => {
          // Update the temporary state while editing
          const value = e.target.value
          const cursorPosition = e.target.selectionStart
          const scrollTop = e.target.scrollTop

          // Check if adding this would exceed the limit
          const inputNamesArray = value.split('\n').filter(name => name.trim() !== '')

          // If we're at or over the limit, prevent adding more names
          if (inputNamesArray.length > MAX_NAMES) {
            // Don't update if it would exceed the limit
            return
          }

          setTempNames(value)

          // Also update the wheel data in real-time
          const currentNamesArray = names.split('\n')

          // Create the new names array by preserving comma information where possible
          const newNamesArray = inputNamesArray.map((inputName: string, index: number) => {
            const trimmedInputName = inputName

            // If we have a corresponding original name at this index
            if (index < currentNamesArray.length) {
              const originalName = currentNamesArray[index]
              const originalWithoutComma = originalName.replace(/,/g, '')

              // If the input matches the original without comma, keep the original
              if (trimmedInputName === originalWithoutComma) {
                return originalName
              }
            }

            // For new or modified names, just return the input (no comma)
            return trimmedInputName
          })

          // Update the wheel data immediately
          onNameChange(undefined, newNamesArray)

          // Check if user typed a comma
          const typedComma = value.charAt(cursorPosition - 1) === ","

          // Preserve scroll position always, but only adjust cursor position if comma was typed
          setTimeout(() => {
            if (inputRef.current) {
              const textarea = inputRef.current as HTMLTextAreaElement
             

              // Only adjust cursor position if user typed a comma
              if (typedComma) {
                textarea.selectionStart = cursorPosition-1
                textarea.selectionEnd = cursorPosition-1
                textarea.scrollTop = scrollTop
              }
            }
          }, 0)
        }}
        onBlur={() => {
          setIsEditing(false)
        }}
        onKeyDown={e => {
          // Handle special keys like Enter
          if (e.key === 'Enter' && !e.shiftKey) {
            // Let the default behavior happen (add a new line)
            // No need to prevent default
          }
        }}
      />
    </ControlsContainer>
  )
}

const ControlsContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  & > * {
    width: 100%;
    margin-bottom: 20px;
  }
`

const ButtonContainer = styled.div`
  width: 100%;
  display: flex;
  gap: 10px;
`

const Button = styled.button`
  padding: 3px 10px;
  border: none;
  border-radius: 8px;
  background-color: #4c4c4c;
  color: #fff;
  border: 1px solid ${props => props.theme.colors.dark};
  font-size: 12px;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    background-color: #999999;
  }
  &:focus {
    outline: none;
  }
`

const Menu = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 10px;
  cursor: pointer;
`

const InputBox = styled.textarea`
  width: 100%;
  height: 100%;
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #bbbbbb;
  border-radius: 5px;
  background-color: transparent;
  color: white;
  font-size: 16px;
  resize: none;
  &:hover {
    border: 1px solid #fff;
  }
  &:focus {
    outline: none;
  }
  &::placeholder {
    color: #ccc;
  }
`

export default Entries
