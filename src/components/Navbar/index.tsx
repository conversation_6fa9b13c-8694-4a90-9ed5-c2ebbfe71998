import { FaPalette, FaSave, FaSearch, FaExpand, FaGlobe, FaCaretDown } from 'react-icons/fa'
import { FaFile, FaFolderOpen, FaShareNodes } from 'react-icons/fa6'
import { useMediaQuery } from '@mui/material'
import { useState } from 'react'

const Navbar = () => {
  const isMobile = useMediaQuery('(max-width: 992px)')
  const [showMobileMenu, setShowMobileMenu] = useState(false)

  return (
    <div className='bg-primary'>
      {/* Main Navbar */}
      <div className='flex items-center justify-between'>
        {/* Logo Section */}
        <div className='flex items-center gap-1 px-4 py-[6px]'>
          <img src='/logo.png' alt='Logo' className='mr-2 h-[38px] w-[38px] rounded-full' />
          <h1 className='flex-1 text-[21px] font-normal tracking-normal'>wheelofnames.com</h1>
        </div>

        {/* Mobile Menu Button */}
        {isMobile ? (
          <button 
            className='px-4 py-[9px]'
            onClick={() => setShowMobileMenu(!showMobileMenu)}
          >
            <div className='space-y-2'>
              <span className='block w-6 h-0.5 bg-white'></span>
              <span className='block w-6 h-0.5 bg-white'></span>
              <span className='block w-6 h-0.5 bg-white'></span>
            </div>
          </button>
        ) : (
          // Desktop Menu
          <div className='flex items-center'>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaPalette />
              <span className='text-[16px] font-[400]'>Customize</span>
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaFile />
              <span className='text-[16px] font-[400]'>New</span>
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaFolderOpen />
              <span className='text-[16px] font-[400]'>Open</span>
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaSave />
              <span className='text-[16px] font-[400]'>Save</span>
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaShareNodes />
              <span className='text-[16px] font-[400]'>Share</span>
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaSearch />
              <span className='text-[16px] font-[400]'>Gallery</span>
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <FaExpand />
            </button>
            <button className='flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
              <span className='text-[16px] font-[400]'>More</span>
              <FaCaretDown className='text-[10px]' />
            </button>
            <button className='flex items-center gap-3 px-4 py-2 transition-colors duration-300 hover:bg-white/15'>
              <FaGlobe />
              <span className='text-[16px] font-[400]'>English</span>
            </button>
          </div>
        )}
      </div>

      {/* Mobile Menu */}
      {isMobile && showMobileMenu && (
        <div className='bg-primary border-t border-white/10'>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaPalette />
            <span className='text-[16px] font-[400]'>Customize</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaFile />
            <span className='text-[16px] font-[400]'>New</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaFolderOpen />
            <span className='text-[16px] font-[400]'>Open</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaSave />
            <span className='text-[16px] font-[400]'>Save</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaShareNodes />
            <span className='text-[16px] font-[400]'>Share</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaSearch />
            <span className='text-[16px] font-[400]'>Gallery</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <FaExpand />
            <span className='text-[16px] font-[400]'>Fullscreen</span>
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-[9px] transition-colors duration-300 hover:bg-white/15'>
            <span className='text-[16px] font-[400]'>More</span>
            <FaCaretDown className='text-[10px]' />
          </button>
          <button className='w-full flex items-center gap-3 px-4 py-2 transition-colors duration-300 hover:bg-white/15'>
            <FaGlobe />
            <span className='text-[16px] font-[400]'>English</span>
          </button>
        </div>
      )}
    </div>
  )
}

export default Navbar
