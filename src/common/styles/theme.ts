// src/common/styles/theme.ts

// Theme object
const theme = {
  colors: {
    black: '#2C251B',
    dark: '#12151c',
    darker: '#191d26',
    primary: '#38B2AC',
    primaryDark: '#319795',
    white: '#f7f7f7'
  },
  fontFamily: {
    poppins: ['Pop<PERSON><PERSON>', 'sans-serif'],
    o<PERSON><PERSON>: ['<PERSON>', 'sans-serif'],
    marlinSoft: ['Marlin Soft','sans-serif']
  },
  fontWeight: {
    light: 200,
    normal: 300,
    medium: 500,
    bold: 700
  },
  spacing: {
    '1': '0.25rem',
    '2': '0.5rem',
    '3': '0.75rem',
    '4': '1rem',
    '5': '1.25rem',
    '6': '1.5rem',
    '7': '1.75rem',
    '8': '2rem'
  }
}

export default theme
